import { combineReducers } from 'redux';
import { ADD_ITEM, DELETE_ITEM, EDIT_ITEM, SET_EDITING_INDEX } from './actions';

const initialItemsState = [];

const itemsReducer = (state = initialItemsState, action) => {
  switch (action.type) {
    case ADD_ITEM:
      return [...state, action.payload];
    case DELETE_ITEM:
      return state.filter((_, i) => i !== action.payload);
    case EDIT_ITEM:
      const newState = [...state];
      newState[action.payload.index] = action.payload.text;
      return newState;
    default:
      return state;
  }
};

const editingReducer = (state = null, action) => {
  switch (action.type) {
    case SET_EDITING_INDEX:
      return action.payload;
    default:
      return state;
  }
};

export default combineReducers({
  items: itemsReducer,
  editingIndex: editingReducer
});