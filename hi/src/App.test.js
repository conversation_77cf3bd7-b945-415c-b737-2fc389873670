import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import App from './App';
import { addItem, deleteItem, editItem, setEditingIndex } from './redux/actions';

// Create mock store
const mockStore = configureStore([]);

describe('CrudComponent', () => {
  let store;

  beforeEach(() => {
    // Initial state for tests
    store = mockStore({
      items: ['Item 1', 'Item 2'],
      editingIndex: null
    });
    store.dispatch = jest.fn();
  });

  test('renders the component with items', () => {
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );
    
    expect(screen.getByText('Simple Api App')).toBeInTheDocument();
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
  });

  test('adds a new item when clicking Add button', () => {
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );
    
    const input = screen.getByPlaceholderText('Add new item...');
    fireEvent.change(input, { target: { value: 'New Item' } });
    
    const addButton = screen.getByText('Add');
    fireEvent.click(addButton);
    
    expect(store.dispatch).toHaveBeenCalledWith(addItem('New Item'));
  });

  test('enters edit mode when clicking Edit button', () => {
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );
    
    const editButtons = screen.getAllByText('Edit');
    fireEvent.click(editButtons[0]); // Edit first item
    
    expect(store.dispatch).toHaveBeenCalledWith(setEditingIndex(0));
  });

  test('deletes an item when clicking Delete button', () => {
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );
    
    const deleteButtons = screen.getAllByText('Delete');
    fireEvent.click(deleteButtons[1]); // Delete second item
    
    expect(store.dispatch).toHaveBeenCalledWith(deleteItem(1));
  });
});
