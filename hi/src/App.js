import React, { useState } from 'react';
import { Provider, useSelector, useDispatch } from 'react-redux';
import { TransitionGroup, CSSTransition } from 'react-transition-group';
import { addItem, deleteItem, editItem, setEditingIndex } from './redux/actions';
import store from './redux/store';
import './App.css';

function CrudComponent() {
  const [input, setInput] = useState('');
  const [editValue, setEditValue] = useState('');
  
  const items = useSelector(state => state.items);
  const editingIndex = useSelector(state => state.editingIndex);
  const dispatch = useDispatch();

  const handleAdd = () => {
    if (input.trim() !== '') {
      dispatch(addItem(input));
      setInput('');
    }
  };

  const handleDelete = (index) => {
    dispatch(deleteItem(index));
  };

  const handleEdit = (index) => {
    dispatch(setEditingIndex(index));
    setEditValue(items[index]);
  };

  const handleUpdate = () => {
    if (editValue.trim() !== '') {
      dispatch(editItem(editingIndex, editValue));
      dispatch(setEditingIndex(null));
      setEditValue('');
    }
  };

  return (
    <div className="CrudApp">
      <h2>Simple Api App</h2>
      <div className="input-container">
        <input
          value={input}
          onChange={e => setInput(e.target.value)}
          placeholder="Add new item..."
          onKeyDown={e => e.key === 'Enter' && handleAdd()}
        />
        <button className="add-btn" onClick={handleAdd}>Add</button>
      </div>
      <TransitionGroup component="ul" className="items-list">
        {items.map((item, idx) => (
          <CSSTransition
            key={idx}
            timeout={400}
            classNames="item"
          >
            <li className="list-item">
              {editingIndex === idx ? (
                <>
                  <input
                    className="edit-input"
                    value={editValue}
                    onChange={e => setEditValue(e.target.value)}
                    onKeyDown={e => e.key === 'Enter' && handleUpdate()}
                    autoFocus
                  />
                  <div className="item-actions">
                    <button className="update-btn" onClick={handleUpdate}>Update</button>
                    <button className="cancel-btn" onClick={() => dispatch(setEditingIndex(null))}>Cancel</button>
                  </div>
                </>
              ) : (
                <>
                  <span className="item-content">{item}</span>
                  <div className="item-actions">
                    <button className="edit-btn" onClick={() => handleEdit(idx)}>Edit</button>
                    <button className="delete-btn" onClick={() => handleDelete(idx)}>Delete</button>
                  </div>
                </>
              )}
            </li>
          </CSSTransition>
        ))}
      </TransitionGroup>
    </div>
  );
}

function App() {
  return (
    <Provider store={store}>
      <CrudComponent />
    </Provider>
  );
}

export default App;
