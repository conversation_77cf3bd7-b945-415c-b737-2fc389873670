.MyApplication {
  text-align: center;
}

.MyApplication-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .MyApplication-logo {
    animation: MyApplication-logo-spin infinite 20s linear;
  }
}

.MyApplication-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.MyApplication-link {
  color: #61dafb;
}

@keyframes MyApplication-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* CRUD App Styles */
.CrudApp {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.CrudApp h2 {
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.input-container {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.CrudApp input {
  flex: 1;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.CrudApp input:focus {
  outline: none;
  border-color: #4CAF50;
}

.CrudApp button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.add-btn {
  background-color: #4CAF50;
  color: white;
}

.add-btn:hover {
  background-color: #45a049;
  transform: translateY(-1px);
}

.edit-btn {
  background-color: #2196F3;
  color: white;
  margin-left: 5px;
}

.edit-btn:hover {
  background-color: #1976D2;
}

.delete-btn {
  background-color: #f44336;
  color: white;
  margin-left: 5px;
}

.delete-btn:hover {
  background-color: #d32f2f;
}

.update-btn {
  background-color: #FF9800;
  color: white;
  margin-left: 5px;
}

.update-btn:hover {
  background-color: #F57C00;
}

.cancel-btn {
  background-color: #9E9E9E;
  color: white;
  margin-left: 5px;
}

.cancel-btn:hover {
  background-color: #757575;
}

.items-list {
  list-style: none;
  padding: 0;
}

.list-item {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  transform: translateY(0);
  opacity: 1;
}

.list-item:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

/* Animation classes */
.item-enter {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
  max-height: 0;
  padding: 0 15px;
  margin-bottom: 0;
  overflow: hidden;
}

.item-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  max-height: 100px;
  padding: 15px;
  margin-bottom: 10px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.item-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
  max-height: 100px;
  padding: 15px;
  margin-bottom: 10px;
}

.item-exit-active {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
  max-height: 0;
  padding: 0 15px;
  margin-bottom: 0;
  transition: all 0.3s ease-in-out;
}

.item-content {
  flex: 1;
  text-align: left;
  font-size: 16px;
  color: #333;
}

.item-actions {
  display: flex;
  gap: 5px;
}

.edit-input {
  flex: 1;
  margin-right: 10px;
}
